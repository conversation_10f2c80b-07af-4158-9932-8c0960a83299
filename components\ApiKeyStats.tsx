import React, { useState, useEffect } from 'react';
import { apiKeyManager } from '../services/apiKeyManager';

interface ApiKeyStatsProps {
    isVisible: boolean;
    onClose: () => void;
}

const ApiKeyStats: React.FC<ApiKeyStatsProps> = ({ isVisible, onClose }) => {
    const [stats, setStats] = useState<Record<string, any>>({});
    const [refreshKey, setRefreshKey] = useState(0);

    useEffect(() => {
        if (isVisible) {
            const loadStats = () => {
                const currentStats = apiKeyManager.getStats();
                setStats(currentStats);
            };

            loadStats();
            const interval = setInterval(loadStats, 2000); // Actualiser toutes les 2 secondes

            return () => clearInterval(interval);
        }
    }, [isVisible, refreshKey]);

    const handleResetBlacklists = () => {
        apiKeyManager.resetBlacklists();
        setRefreshKey(prev => prev + 1);
    };

    if (!isVisible) return null;

    const totalKeys = Object.keys(stats).length;
    const activeKeys = Object.values(stats).filter((stat: any) => !stat.isBlacklisted).length;
    const blacklistedKeys = totalKeys - activeKeys;

    return (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="bg-slate-800/95 backdrop-blur-sm rounded-2xl p-6 max-w-4xl max-h-[80vh] overflow-y-auto border border-slate-700 shadow-2xl">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">📊 Statistiques des Clés API</h2>
                    <button
                        onClick={onClose}
                        className="text-slate-400 hover:text-slate-200 text-xl transition-colors"
                    >
                        ✕
                    </button>
                </div>

                {/* Résumé global */}
                <div className="grid grid-cols-3 gap-4 mb-6">
                    <div className="bg-slate-700/50 border border-slate-600 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-indigo-400">{totalKeys}</div>
                        <div className="text-sm text-slate-300">Clés Totales</div>
                    </div>
                    <div className="bg-slate-700/50 border border-slate-600 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-emerald-400">{activeKeys}</div>
                        <div className="text-sm text-slate-300">Clés Actives</div>
                    </div>
                    <div className="bg-slate-700/50 border border-slate-600 p-4 rounded-lg text-center">
                        <div className="text-2xl font-bold text-red-400">{blacklistedKeys}</div>
                        <div className="text-sm text-slate-300">Clés Bloquées</div>
                    </div>
                </div>

                {/* Actions */}
                <div className="mb-4 flex gap-2">
                    <button
                        onClick={handleResetBlacklists}
                        className="bg-yellow-600/80 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors border border-yellow-500/30"
                    >
                        🔄 Réinitialiser les Blocages
                    </button>
                    <button
                        onClick={() => setRefreshKey(prev => prev + 1)}
                        className="bg-indigo-600/80 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg transition-colors border border-indigo-500/30"
                    >
                        🔄 Actualiser
                    </button>
                </div>

                {/* Détails par clé */}
                <div className="space-y-3">
                    {Object.entries(stats).map(([keyName, stat]: [string, any]) => {
                        const successRate = stat.totalRequests > 0 
                            ? ((stat.successfulRequests / stat.totalRequests) * 100).toFixed(1)
                            : '0.0';

                        const isBlacklisted = stat.isBlacklisted;
                        const timeUntilUnblock = stat.blacklistUntil 
                            ? Math.max(0, Math.ceil((stat.blacklistUntil - Date.now()) / 1000))
                            : 0;

                        return (
                            <div
                                key={keyName}
                                className={`p-4 rounded-lg border ${
                                    isBlacklisted
                                        ? 'bg-red-900/20 border-red-500/30'
                                        : 'bg-emerald-900/20 border-emerald-500/30'
                                }`}
                            >
                                <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                        <h3 className="font-semibold text-lg text-slate-200">
                                            {isBlacklisted ? '🚫' : '✅'} {keyName}
                                        </h3>
                                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2 text-sm">
                                            <div>
                                                <span className="font-medium text-slate-300">Requêtes:</span>
                                                <div className="text-slate-200">{stat.totalRequests}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium text-slate-300">Succès:</span>
                                                <div className="text-emerald-400">{stat.successfulRequests}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium text-slate-300">Échecs:</span>
                                                <div className="text-red-400">{stat.failedRequests}</div>
                                            </div>
                                            <div>
                                                <span className="font-medium text-slate-300">Taux de succès:</span>
                                                <div className={`font-bold ${
                                                    parseFloat(successRate) > 80 ? 'text-emerald-400' :
                                                    parseFloat(successRate) > 50 ? 'text-yellow-400' : 'text-red-400'
                                                }`}>
                                                    {successRate}%
                                                </div>
                                            </div>
                                        </div>

                                        {/* Erreurs par type */}
                                        {Object.keys(stat.errorTypes).length > 0 && (
                                            <div className="mt-2">
                                                <span className="font-medium text-sm text-slate-300">Erreurs:</span>
                                                <div className="flex flex-wrap gap-2 mt-1">
                                                    {Object.entries(stat.errorTypes).map(([errorCode, count]: [string, any]) => (
                                                        <span
                                                            key={errorCode}
                                                            className="bg-red-900/30 text-red-400 border border-red-500/30 px-2 py-1 rounded text-xs"
                                                        >
                                                            {errorCode}: {count}
                                                        </span>
                                                    ))}
                                                </div>
                                            </div>
                                        )}

                                        {/* Statut de blocage */}
                                        {isBlacklisted && (
                                            <div className="mt-2">
                                                <span className="bg-red-900/30 text-red-400 border border-red-500/30 px-2 py-1 rounded text-sm">
                                                    {timeUntilUnblock > 0
                                                        ? `Bloquée pour ${timeUntilUnblock}s`
                                                        : 'Bloquée (échecs consécutifs)'
                                                    }
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {Object.keys(stats).length === 0 && (
                    <div className="text-center text-slate-400 py-8">
                        Aucune statistique disponible. Effectuez quelques requêtes pour voir les données.
                    </div>
                )}
            </div>
        </div>
    );
};

export default ApiKeyStats;
