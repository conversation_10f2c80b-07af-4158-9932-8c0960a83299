import React, { useRef, useEffect, useState } from 'react';
import type { Message } from '../types';

interface ChatInterfaceProps {
  conversation: Message[];
  isProcessing: boolean;
}

const AILogo: React.FC = () => (
    <div className="w-8 h-8 rounded-full bg-gradient-to-tr from-purple-500 to-indigo-600 flex items-center justify-center flex-shrink-0">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    </div>
);

const UserLogo: React.FC = () => (
    <div className="w-8 h-8 rounded-full bg-slate-600 flex items-center justify-center flex-shrink-0">
        <svg xmlns="http://www.w.org/2000/svg" className="h-5 w-5 text-slate-300" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
        </svg>
    </div>
);

const ThinkingMascot: React.FC = () => (
    <div className="flex flex-col items-center justify-center p-4">
        <img
            src="https://res.cloudinary.com/dte5zykne/image/upload/f_auto,q_auto/v1756018024/Rory-9_xvs7jb.gif"
            alt="Agent en pleine réflexion..."
            className="w-48 h-48"
        />
        <p className="mt-2 text-slate-400 animate-pulse">Phase de réflexion...</p>
    </div>
);

const CopyButton: React.FC<{ text: string; messageIndex: number }> = ({ text, messageIndex }) => {
    const [copied, setCopied] = useState(false);

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Erreur lors de la copie:', err);
        }
    };

    return (
        <button
            onClick={handleCopy}
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1.5 rounded-md hover:bg-slate-600/50 text-slate-400 hover:text-slate-200"
            title={copied ? "Copié !" : "Copier le message"}
        >
            {copied ? (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
            ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
            )}
        </button>
    );
};


export const ChatInterface: React.FC<ChatInterfaceProps> = ({ conversation, isProcessing }) => {
  const chatEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversation, isProcessing]);

  const cleanThinkingSymbols = (text: string): string => {
    // Supprimer les symboles de réflexion bizarres
    return text
        .replace(/◁think▷/gi, '')
        .replace(/<think>/gi, '')
        .replace(/<\/think>/gi, '')
        .replace(/\[think\]/gi, '')
        .replace(/\[\/think\]/gi, '')
        .replace(/◁.*?▷/g, '')
        .replace(/【.*?】/g, '')
        .trim();
  };

  const formatText = (text: string) => {
    // Nettoyer d'abord les symboles bizarres
    const cleanedText = cleanThinkingSymbols(text);
    // Basic markdown for bolding and lists
    const bolded = cleanedText.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    const lists = bolded.replace(/^\s*[\-\*]\s(.*)/gm, '<li class="ml-4 list-disc">$1</li>');
    return { __html: lists.replace(/\n/g, '<br />') };
  };

  return (
    <div className="flex-grow p-6 overflow-y-auto space-y-6">
      {conversation.map((message, index) => (
        <div key={index} className={`group flex items-start gap-4 ${message.sender === 'user' ? 'justify-end' : ''}`}>
          {message.sender === 'ai' && <AILogo />}
          <div className={`relative max-w-xl p-4 rounded-2xl ${message.sender === 'ai' ? 'bg-slate-700 text-slate-200 rounded-tl-none' : 'bg-indigo-600 text-white rounded-br-none'}`}>
             <div className="prose prose-invert prose-sm max-w-none" dangerouslySetInnerHTML={formatText(message.text)} />
             <div className={`absolute top-2 ${message.sender === 'user' ? 'left-2' : 'right-2'}`}>
               <CopyButton text={message.text} messageIndex={index} />
             </div>
          </div>
          {message.sender === 'user' && <UserLogo />}
        </div>
      ))}
      {isProcessing && <ThinkingMascot />}
      <div ref={chatEndRef} />
    </div>
  );
};