import React, { useState } from 'react';
import { workflowMemoryService, type FinalActionPlan } from '../services/workflowMemoryService';

interface FinalActionPlanGeneratorProps {
  initialProblem: string;
  currentStepIndex: number;
  className?: string;
}

export const FinalActionPlanGenerator: React.FC<FinalActionPlanGeneratorProps> = ({
  initialProblem,
  currentStepIndex,
  className = ''
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [finalPlan, setFinalPlan] = useState<FinalActionPlan | null>(null);
  const [showPlan, setShowPlan] = useState(false);

  const handleGeneratePlan = async () => {
    setIsGenerating(true);
    try {
      const plan = await workflowMemoryService.generateFinalActionPlan(initialProblem);
      setFinalPlan(plan);
      setShowPlan(true);
    } catch (error) {
      console.error('Erreur lors de la génération du plan:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleExportPlan = (format: 'md' | 'json') => {
    if (!finalPlan) return;

    let content = '';
    let mimeType = '';
    let filename = '';

    if (format === 'md') {
      content = `# Plan d'Action Final - ${new Date().toLocaleDateString('fr-FR')}

## Résumé Exécutif
${finalPlan.executiveSummary}

## Principales Découvertes
${finalPlan.keyFindings.map((finding, index) => `${index + 1}. ${finding}`).join('\n')}

## Actions Prioritaires

### Actions Haute Priorité 🔴
${finalPlan.prioritizedActions.filter(a => a.priority === 'HIGH').map(action => 
  `- **${action.action}**\n  - Justification: ${action.rationale}\n  - Délai: ${action.timeline}`
).join('\n\n')}

### Actions Priorité Moyenne 🟡
${finalPlan.prioritizedActions.filter(a => a.priority === 'MEDIUM').map(action => 
  `- **${action.action}**\n  - Justification: ${action.rationale}\n  - Délai: ${action.timeline}`
).join('\n\n')}

### Actions Priorité Faible 🟢
${finalPlan.prioritizedActions.filter(a => a.priority === 'LOW').map(action => 
  `- **${action.action}**\n  - Justification: ${action.rationale}\n  - Délai: ${action.timeline}`
).join('\n\n')}

## Prochaines Étapes
${finalPlan.nextSteps.map((step, index) => `${index + 1}. ${step}`).join('\n')}

## Métriques de Succès
${finalPlan.successMetrics.map((metric, index) => `${index + 1}. ${metric}`).join('\n')}

## Risques Potentiels
${finalPlan.potentialRisks.map((risk, index) => `${index + 1}. ${risk}`).join('\n')}

---
*Généré automatiquement par Roony Agent le ${new Date().toLocaleString('fr-FR')}*`;
      
      mimeType = 'text/markdown';
      filename = `plan-action-final-${new Date().toISOString().split('T')[0]}.md`;
    } else {
      content = JSON.stringify(finalPlan, null, 2);
      mimeType = 'application/json';
      filename = `plan-action-final-${new Date().toISOString().split('T')[0]}.json`;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Ne pas afficher si pas assez d'étapes complétées
  if (currentStepIndex < 3) {
    return null;
  }

  const insights = workflowMemoryService.getAllInsights();

  return (
    <div className={`${className} p-4 bg-slate-800/50 rounded-2xl shadow-lg border border-slate-700`}>
      <div className="flex items-center mb-3">
        <svg className="w-5 h-5 mr-2 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 className="text-sm font-semibold text-emerald-300">Plan d'Action Final</h3>
      </div>

      <p className="text-xs text-slate-400 mb-3">
        Générez un plan d'action détaillé basé sur {insights.length} analyses d'étapes
      </p>

      {!showPlan ? (
        <button
          onClick={handleGeneratePlan}
          disabled={isGenerating}
          className="w-full bg-emerald-600/80 hover:bg-emerald-600 disabled:bg-slate-600 text-white text-sm font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-2"
        >
          {isGenerating ? (
            <>
              <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Génération...
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Générer Plan Final
            </>
          )}
        </button>
      ) : finalPlan && (
        <div className="space-y-3">
          <div className="bg-slate-700/50 p-3 rounded-lg">
            <h4 className="text-xs font-semibold text-emerald-300 mb-2">Résumé</h4>
            <p className="text-xs text-slate-300">{finalPlan.executiveSummary}</p>
          </div>

          <div className="bg-slate-700/50 p-3 rounded-lg">
            <h4 className="text-xs font-semibold text-red-300 mb-2">
              Actions Prioritaires ({finalPlan.prioritizedActions.filter(a => a.priority === 'HIGH').length})
            </h4>
            <ul className="space-y-1">
              {finalPlan.prioritizedActions.filter(a => a.priority === 'HIGH').slice(0, 3).map((action, index) => (
                <li key={index} className="text-xs text-slate-300 flex items-start gap-2">
                  <span className="text-red-400 mt-0.5">•</span>
                  <span>{action.action}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => handleExportPlan('md')}
              className="flex-1 bg-slate-600/80 hover:bg-slate-600 text-white text-xs font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-1"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              MD
            </button>
            <button
              onClick={() => handleExportPlan('json')}
              className="flex-1 bg-slate-600/80 hover:bg-slate-600 text-white text-xs font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-1"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              JSON
            </button>
            <button
              onClick={() => setShowPlan(false)}
              className="flex-1 bg-indigo-600/80 hover:bg-indigo-600 text-white text-xs font-semibold py-2 px-3 rounded transition-colors flex items-center justify-center gap-1"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Nouveau
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
