import React, { useEffect, useRef, useState, useCallback } from 'react';
import { gsap } from 'gsap';
import { roonyPreloader } from '../utils/roonyPreloader';

// Types pour les différentes animations de Roony
export type RoonyAnimationType =
  | 'greeting'      // Salutation - bonjour en bougeant la main
  | 'idea'          // Content avec une idée 💡
  | 'pointing-up'   // Pointe vers le haut pour cibler quelque chose
  | 'pointing-right'// Pointe vers la droite pour montrer quelque chose
  | 'disagreement'  // Mécontentement ou désaccord
  | 'typing'        // Recherche sérieuse sur clavier
  | 'proud'         // Heureux et fier de lui (pouce vers lui-même)
  | 'idle';         // État de repos (pas d'animation)

// Configuration des animations avec leurs URLs Cloudinary
const ROONY_ANIMATIONS = {
  greeting: 'https://res.cloudinary.com/dte5zykne/image/upload/v1756018014/Rory-1_mv9r9e.gif',
  idea: 'https://res.cloudinary.com/dte5zykne/image/upload/v1756018023/Rory-8_mkgqpz.gif',
  'pointing-up': 'https://res.cloudinary.com/dte5zykne/image/upload/v1756018014/Rory-2_tnbjew.gif',
  'pointing-right': 'https://res.cloudinary.com/dte5zykne/image/upload/v1756018013/Rory-4_ignnks.gif',
  disagreement: 'https://res.cloudinary.com/dte5zykne/image/upload/v1756018012/Rory-3_hhtgop.gif',
  typing: 'https://res.cloudinary.com/dte5zykne/image/upload/v1756018012/Rory-6_tcwihe.gif',
  proud: 'https://res.cloudinary.com/dte5zykne/image/upload/v1756018011/Rory-5_vdgcre.gif'
} as const;

interface RoonyMascotProps {
  /** Type d'animation à jouer */
  animation: RoonyAnimationType;
  /** Taille du composant (en pixels) */
  size?: number;
  /** Position du composant */
  position?: 'static' | 'fixed' | 'absolute' | 'relative';
  /** Classes CSS additionnelles */
  className?: string;
  /** Callback appelé quand l'animation se termine */
  onAnimationComplete?: () => void;
  /** Durée d'affichage de l'animation (en ms) - 0 pour infini */
  duration?: number;
  /** Animation d'entrée GSAP */
  entranceAnimation?: 'fadeIn' | 'slideInLeft' | 'slideInRight' | 'bounceIn' | 'none';
  /** Animation de sortie GSAP */
  exitAnimation?: 'fadeOut' | 'slideOutLeft' | 'slideOutRight' | 'bounceOut' | 'none';
}

export const RoonyMascot: React.FC<RoonyMascotProps> = ({
  animation,
  size = 120,
  position = 'relative',
  className = '',
  onAnimationComplete,
  duration = 3000, // 3 secondes par défaut
  entranceAnimation = 'fadeIn',
  exitAnimation = 'fadeOut'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [currentImageSrc, setCurrentImageSrc] = useState<string>('');
  const [isImageLoaded, setIsImageLoaded] = useState(false);

  // Fonction pour jouer l'animation d'entrée (optimisée avec useCallback)
  const playEntranceAnimation = useCallback(() => {
    if (!containerRef.current || !isImageLoaded) return;

    const container = containerRef.current;

    // Protection contre les conflits de scroll - désactiver les interactions pendant l'animation
    const originalOverflow = document.body.style.overflow;

    switch (entranceAnimation) {
      case 'fadeIn':
        gsap.fromTo(container,
          { opacity: 0, scale: 0.8 },
          {
            opacity: 1,
            scale: 1,
            duration: 0.5,
            ease: "back.out(1.7)",
            onComplete: () => {
              // Restaurer le scroll après l'animation
              document.body.style.overflow = originalOverflow;
            }
          }
        );
        break;
      case 'slideInLeft':
        gsap.fromTo(container,
          { x: -100, opacity: 0 },
          {
            x: 0,
            opacity: 1,
            duration: 0.6,
            ease: "power2.out",
            onComplete: () => {
              document.body.style.overflow = originalOverflow;
            }
          }
        );
        break;
      case 'slideInRight':
        gsap.fromTo(container,
          { x: 100, opacity: 0 },
          {
            x: 0,
            opacity: 1,
            duration: 0.6,
            ease: "power2.out",
            onComplete: () => {
              document.body.style.overflow = originalOverflow;
            }
          }
        );
        break;
      case 'bounceIn':
        gsap.fromTo(container,
          { scale: 0, opacity: 0 },
          {
            scale: 1,
            opacity: 1,
            duration: 0.8,
            ease: "bounce.out",
            onComplete: () => {
              document.body.style.overflow = originalOverflow;
            }
          }
        );
        break;
      case 'none':
        gsap.set(container, { opacity: 1, scale: 1 });
        break;
    }
  }, [isImageLoaded]);

  // Fonction pour jouer l'animation de sortie (optimisée avec useCallback)
  const playExitAnimation = useCallback(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    
    const exitPromise = new Promise<void>((resolve) => {
      switch (exitAnimation) {
        case 'fadeOut':
          gsap.to(container, {
            opacity: 0,
            scale: 0.8,
            duration: 0.4,
            ease: "power2.in",
            onComplete: resolve
          });
          break;
        case 'slideOutLeft':
          gsap.to(container, {
            x: -100,
            opacity: 0,
            duration: 0.5,
            ease: "power2.in",
            onComplete: resolve
          });
          break;
        case 'slideOutRight':
          gsap.to(container, {
            x: 100,
            opacity: 0,
            duration: 0.5,
            ease: "power2.in",
            onComplete: resolve
          });
          break;
        case 'bounceOut':
          gsap.to(container, {
            scale: 0,
            opacity: 0,
            duration: 0.6,
            ease: "back.in(1.7)",
            onComplete: resolve
          });
          break;
        case 'none':
          gsap.set(container, { opacity: 0 });
          resolve();
          break;
      }
    });

    return exitPromise;
  }, []);

  // Effet pour gérer le changement d'animation
  useEffect(() => {
    if (animation === 'idle') {
      setIsVisible(false);
      setIsImageLoaded(false);
      return;
    }

    // Définir la nouvelle source d'image
    const newImageSrc = ROONY_ANIMATIONS[animation];
    setCurrentImageSrc(newImageSrc);
    setIsVisible(true);
    setIsImageLoaded(false);

    // Vérifier si l'image est préchargée pour une meilleure performance
    if (roonyPreloader.isPreloaded(newImageSrc)) {
      setIsImageLoaded(true);
      // Jouer l'animation d'entrée immédiatement si préchargée
      const entranceTimer = setTimeout(() => {
        playEntranceAnimation();
      }, 50);

      // Programmer la sortie si une durée est définie
      let exitTimer: NodeJS.Timeout;
      if (duration > 0) {
        exitTimer = setTimeout(async () => {
          await playExitAnimation();
          setIsVisible(false);
          onAnimationComplete?.();
        }, duration);
      }

      return () => {
        clearTimeout(entranceTimer);
        if (exitTimer) clearTimeout(exitTimer);
      };
    }

  }, [animation, duration, entranceAnimation, exitAnimation, onAnimationComplete, playEntranceAnimation, playExitAnimation]);

  // Effet séparé pour gérer le chargement d'image quand elle n'est pas préchargée
  useEffect(() => {
    if (!currentImageSrc || roonyPreloader.isPreloaded(currentImageSrc)) {
      return;
    }

    // L'image n'est pas préchargée, attendre qu'elle se charge
    const img = new Image();
    img.onload = () => {
      setIsImageLoaded(true);

      // Jouer l'animation d'entrée après chargement
      const entranceTimer = setTimeout(() => {
        playEntranceAnimation();
      }, 100);

      // Programmer la sortie si une durée est définie
      let exitTimer: NodeJS.Timeout;
      if (duration > 0) {
        exitTimer = setTimeout(async () => {
          await playExitAnimation();
          setIsVisible(false);
          onAnimationComplete?.();
        }, duration);
      }

      return () => {
        clearTimeout(entranceTimer);
        if (exitTimer) clearTimeout(exitTimer);
      };
    };

    img.onerror = () => {
      console.error(`Erreur de chargement de l'animation Roony: ${currentImageSrc}`);
      setIsImageLoaded(false);
    };

    img.src = currentImageSrc;
  }, [currentImageSrc, duration, onAnimationComplete, playEntranceAnimation, playExitAnimation]);

  // Ne pas rendre le composant si pas visible ou pas d'animation
  if (!isVisible || animation === 'idle' || !currentImageSrc) {
    return null;
  }

  return (
    <div
      ref={containerRef}
      className={`roony-mascot ${className}`}
      style={{
        position,
        width: size,
        height: size,
        opacity: 0, // Initialement invisible pour l'animation d'entrée
        zIndex: 1000, // S'assurer que Roony est au-dessus des autres éléments
      }}
    >
      <img
        ref={imageRef}
        src={currentImageSrc}
        alt={`Roony - ${animation}`}
        className="w-full h-full object-contain rounded-lg shadow-lg"
        style={{
          filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))',
        }}
        onError={(e) => {
          console.error(`Erreur de chargement de l'animation Roony: ${animation}`, e);
          setIsImageLoaded(false);
        }}
        onLoad={() => {
          // Image chargée avec succès
          console.log(`Animation Roony chargée: ${animation}`);
          if (!roonyPreloader.isPreloaded(currentImageSrc)) {
            setIsImageLoaded(true);
          }
        }}
      />
    </div>
  );
};

export default RoonyMascot;
