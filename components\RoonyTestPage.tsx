import React, { useState, useRef } from 'react';
import RoonyMascot, { type RoonyAnimationType } from './RoonyMascot';
import RoonyContextualMascot, { type RoonyContextualMascotRef } from './RoonyContextualMascot';
import RoonyInlineAnimation from './RoonyInlineAnimation';
import { type RoonyContextType } from '../hooks/useRoonyAnimations';
import { useRoonyPreloader } from '../utils/roonyPreloader';

/**
 * Page de test pour toutes les animations de Roony
 * Utile pour le développement et les démonstrations
 */
export const RoonyTestPage: React.FC = () => {
  const [selectedAnimation, setSelectedAnimation] = useState<RoonyAnimationType>('idle');
  const [selectedContext, setSelectedContext] = useState<RoonyContextType>('workflow-start');
  const [showInlineAnimation, setShowInlineAnimation] = useState(false);
  const [animationSize, setAnimationSize] = useState(120);
  
  const roonyRef = useRef<RoonyContextualMascotRef>(null);
  const { getProgress, getLoadedCount, getTotalCount, preloadAll } = useRoonyPreloader();

  const animations: RoonyAnimationType[] = [
    'greeting', 'idea', 'pointing-up', 'pointing-right', 
    'disagreement', 'typing', 'slide-point', 'slide-thumbs', 'proud'
  ];

  const contexts: RoonyContextType[] = [
    'workflow-start', 'step-complete', 'idea-found', 'processing',
    'error', 'final-result', 'user-input', 'pointing-suggestion',
    'celebration', 'thinking'
  ];

  const handleAnimationTest = (animation: RoonyAnimationType) => {
    setSelectedAnimation(animation);
    setTimeout(() => setSelectedAnimation('idle'), 4000);
  };

  const handleContextTest = (context: RoonyContextType) => {
    setSelectedContext(context);
    roonyRef.current?.triggerContext(context);
  };

  const handleInlineTest = () => {
    setShowInlineAnimation(true);
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8 text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-indigo-500">
          🎬 Test des Animations Roony
        </h1>

        {/* Statistiques de préchargement */}
        <div className="bg-slate-800 rounded-lg p-4 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-400">📊 Statut du Préchargement</h2>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-400">{getProgress()}%</div>
              <div className="text-sm text-slate-400">Progression</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-400">{getLoadedCount()}</div>
              <div className="text-sm text-slate-400">Chargées</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-400">{getTotalCount()}</div>
              <div className="text-sm text-slate-400">Total</div>
            </div>
          </div>
          <button
            onClick={preloadAll}
            className="mt-4 bg-blue-600 hover:bg-blue-500 px-4 py-2 rounded-md transition-colors"
          >
            Précharger Toutes les Animations
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Test des animations directes */}
          <div className="bg-slate-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-yellow-400">🎭 Animations Directes</h2>
            
            <div className="mb-4">
              <label className="block text-sm font-medium mb-2">Taille: {animationSize}px</label>
              <input
                type="range"
                min="60"
                max="200"
                value={animationSize}
                onChange={(e) => setAnimationSize(Number(e.target.value))}
                className="w-full"
              />
            </div>

            <div className="grid grid-cols-3 gap-2 mb-6">
              {animations.map((animation) => (
                <button
                  key={animation}
                  onClick={() => handleAnimationTest(animation)}
                  className="bg-slate-700 hover:bg-slate-600 px-3 py-2 rounded-md text-sm transition-colors"
                >
                  {animation}
                </button>
              ))}
            </div>

            <div className="flex justify-center h-40 items-center bg-slate-900 rounded-lg">
              <RoonyMascot
                animation={selectedAnimation}
                size={animationSize}
                duration={3500}
                entranceAnimation="bounceIn"
                exitAnimation="fadeOut"
              />
            </div>
          </div>

          {/* Test des contextes */}
          <div className="bg-slate-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-cyan-400">🎯 Animations Contextuelles</h2>
            
            <div className="grid grid-cols-2 gap-2 mb-6">
              {contexts.map((context) => (
                <button
                  key={context}
                  onClick={() => handleContextTest(context)}
                  className="bg-slate-700 hover:bg-slate-600 px-3 py-2 rounded-md text-xs transition-colors"
                >
                  {context}
                </button>
              ))}
            </div>

            <div className="text-center text-slate-400">
              <p>Les animations contextuelles apparaissent en bas à droite</p>
              <p className="text-xs mt-2">Contexte actuel: <span className="text-cyan-300">{selectedContext}</span></p>
            </div>
          </div>

          {/* Test des animations inline */}
          <div className="bg-slate-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-pink-400">✨ Animations Inline</h2>
            
            <button
              onClick={handleInlineTest}
              className="bg-pink-600 hover:bg-pink-500 px-4 py-2 rounded-md transition-colors mb-4"
            >
              Déclencher Animation Inline
            </button>

            <div className="flex justify-center items-center h-32 bg-slate-900 rounded-lg relative">
              <span className="text-slate-500">Zone d'animation inline</span>
              
              <RoonyInlineAnimation
                trigger={showInlineAnimation}
                animation="idea"
                size={80}
                duration={3000}
                className="absolute"
                onComplete={() => setShowInlineAnimation(false)}
              />
            </div>
          </div>

          {/* Informations */}
          <div className="bg-slate-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-emerald-400">ℹ️ Informations</h2>
            
            <div className="space-y-3 text-sm">
              <div>
                <span className="font-semibold text-emerald-300">Animations disponibles:</span>
                <span className="text-slate-300"> {animations.length}</span>
              </div>
              <div>
                <span className="font-semibold text-emerald-300">Contextes disponibles:</span>
                <span className="text-slate-300"> {contexts.length}</span>
              </div>
              <div>
                <span className="font-semibold text-emerald-300">Préchargement:</span>
                <span className="text-slate-300"> Automatique</span>
              </div>
              <div>
                <span className="font-semibold text-emerald-300">Performance:</span>
                <span className="text-slate-300"> Optimisée avec GSAP</span>
              </div>
            </div>

            <div className="mt-4 p-3 bg-slate-900 rounded-md">
              <p className="text-xs text-slate-400">
                💡 <strong>Astuce:</strong> Les animations contextuelles combinent plusieurs animations 
                pour créer des séquences plus riches et expressives.
              </p>
            </div>
          </div>
        </div>

        {/* Mascotte contextuelle (toujours présente) */}
        <RoonyContextualMascot
          ref={roonyRef}
          position="fixed"
          size={120}
          customPosition={{ bottom: 20, right: 20 }}
        />
      </div>
    </div>
  );
};

export default RoonyTestPage;
