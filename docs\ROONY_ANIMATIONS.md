# 🎬 Système d'Animations Roony - Documentation

## Vue d'ensemble

Le système d'animations Roony apporte de l'interactivité et de la personnalité à l'application Studio Agentique. Roony, la mascotte de l'application, réagit contextuellement aux actions de l'utilisateur et aux événements du workflow.

## 🎭 Types d'Animations Disponibles

### Animations de Base
- **`greeting`** - Salutation avec la main (Rory-1)
- **`idea`** - Content avec une idée 💡 (Rory-8)
- **`pointing-up`** - Pointe vers le haut (Rory-2)
- **`pointing-right`** - Pointe vers la droite (Rory-4)
- **`disagreement`** - Mécontentement (Rory-3)
- **`typing`** - Recherche sérieuse sur clavier (Rory-6)
- **`proud`** - He<PERSON><PERSON> et fier de lui (Rory-5)
- **`idle`** - État de repos (pas d'animation)

### 🔄 Système de Rotation pour la Réflexion
Les animations de réflexion (`thinking` et `processing`) utilisent désormais un système de rotation intelligent qui alterne entre :
- `idea` - Content avec une idée 💡
- `typing` - Recherche sérieuse sur clavier
- `greeting` - Salutation amicale
- `proud` - Fier et confiant
- `pointing-up` - Pointe vers le haut (inspiration)

## 🏗️ Architecture du Système

### Composants Principaux

1. **`RoonyMascot`** - Composant de base pour afficher une animation
2. **`RoonyContextualMascot`** - Mascotte positionnée avec contrôle contextuel
3. **`RoonyInlineAnimation`** - Animation inline pour intégration dans l'interface
4. **`useRoonyAnimations`** - Hook pour gérer les animations contextuelles

### Utilitaires

- **`roonyPreloader`** - Système de préchargement des GIFs pour optimiser les performances

## 🚀 Utilisation

### Composant de Base

```tsx
import RoonyMascot from './components/RoonyMascot';

<RoonyMascot
  animation="greeting"
  size={120}
  duration={3000}
  entranceAnimation="bounceIn"
  exitAnimation="fadeOut"
  onAnimationComplete={() => console.log('Animation terminée')}
/>
```

### Mascotte Contextuelle (Positionnée)

```tsx
import RoonyContextualMascot from './components/RoonyContextualMascot';

const roonyRef = useRef<RoonyContextualMascotRef>(null);

<RoonyContextualMascot
  ref={roonyRef}
  position="fixed"
  size={140}
  customPosition={{ bottom: 80, right: 20 }}
/>

// Déclencher une animation
roonyRef.current?.triggerContext('workflow-start');
```

### Animation Inline

```tsx
import RoonyInlineAnimation from './components/RoonyInlineAnimation';

<RoonyInlineAnimation
  trigger={showCelebration}
  animation="proud"
  size={100}
  duration={3500}
  onComplete={() => setShowCelebration(false)}
/>
```

## 🎯 Contextes d'Animation

Le hook `useRoonyAnimations` propose des contextes prédéfinis :

- **`workflow-start`** - Début du workflow (salutation + pointage)
- **`step-complete`** - Étape terminée (pouce levé)
- **`idea-found`** - Idée trouvée (ampoule)
- **`processing`** - Traitement en cours (frappe au clavier)
- **`error`** - Erreur (mécontentement)
- **`final-result`** - Résultat final (fierté)
- **`celebration`** - Célébration (combinaison d'animations)
- **`thinking`** - Réflexion (frappe + idée)

## ⚡ Optimisations de Performance

### Préchargement Automatique

Le système précharge automatiquement les animations :

1. **Animations prioritaires** - Chargées immédiatement au démarrage
2. **Toutes les animations** - Chargées en arrière-plan après 2 secondes

### Animations Prioritaires

- Salutation (`greeting`)
- Idée (`idea`)
- Frappe (`typing`)
- Pouce levé (`slide-thumbs`)
- Fierté (`proud`)

### Utilisation du Préchargeur

```tsx
import { useRoonyPreloader } from '../utils/roonyPreloader';

const { preloadAll, getProgress, isPreloaded } = useRoonyPreloader();

// Précharger toutes les animations
await preloadAll();

// Vérifier le statut
const progress = getProgress(); // 0-100%
const loaded = isPreloaded(url);
```

## 🎨 Animations d'Entrée/Sortie

### Types d'Entrée
- **`fadeIn`** - Apparition en fondu
- **`slideInLeft`** - Glissement depuis la gauche
- **`slideInRight`** - Glissement depuis la droite
- **`bounceIn`** - Rebond d'entrée
- **`none`** - Pas d'animation

### Types de Sortie
- **`fadeOut`** - Disparition en fondu
- **`slideOutLeft`** - Glissement vers la gauche
- **`slideOutRight`** - Glissement vers la droite
- **`bounceOut`** - Rebond de sortie
- **`none`** - Pas d'animation

## 🔧 Configuration

### Tailles Recommandées
- **Page d'accueil** : 150px
- **Mascotte contextuelle** : 140px
- **Animations inline** : 80-100px
- **Petites animations** : 60px

### Durées Recommandées
- **Salutation** : 2-4 secondes
- **Célébration** : 3-4 secondes
- **Feedback rapide** : 2-2.5 secondes
- **Animations complexes** : 4-5 secondes

## 📍 Intégrations Actuelles

### Page d'Accueil
- Animation de salutation au-dessus du titre

### Workflow Principal
- Début de workflow (salutation + pointage)
- Traitement en cours (frappe au clavier)
- Étapes complétées (pouce levé)
- Erreurs (mécontentement)
- Résultat final (fierté)

### Résultat Final
- Animation de célébration inline dans le titre

### Mascotte Contextuelle
- Positionnée en bas à droite
- Réagit aux événements de l'application

## 🛠️ Développement

### Ajouter une Nouvelle Animation

1. Ajouter l'URL dans `ROONY_ANIMATIONS` (RoonyMascot.tsx)
2. Ajouter le type dans `RoonyAnimationType`
3. Mettre à jour le préchargeur si nécessaire
4. Créer un contexte dans `useRoonyAnimations` si approprié

### Bonnes Pratiques

- Utiliser le préchargement pour les animations fréquentes
- Préférer les contextes prédéfinis aux animations manuelles
- Tester les performances sur connexions lentes
- Respecter les durées recommandées
- Utiliser les animations d'entrée/sortie appropriées

## 🐛 Dépannage

### Animation ne s'affiche pas
- Vérifier que l'URL de l'animation est correcte
- S'assurer que le composant est visible (`isVisible: true`)
- Vérifier les erreurs de réseau dans la console

### Performance lente
- Utiliser le préchargement
- Réduire la taille des animations
- Limiter le nombre d'animations simultanées

### Animation se répète
- Vérifier le paramètre `autoReset`
- S'assurer que les triggers sont correctement gérés

---

**Version** : 1.0  
**Dernière mise à jour** : Janvier 2025  
**Développé par** : FlexoDiv pour Cisco
