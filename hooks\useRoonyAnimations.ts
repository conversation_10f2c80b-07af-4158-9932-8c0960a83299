import { useState, useCallback, useRef } from 'react';
import type { RoonyAnimationType } from '../components/RoonyMascot';

interface RoonyAnimationState {
  currentAnimation: RoonyAnimationType;
  isVisible: boolean;
  position: { x: number; y: number } | null;
}

interface UseRoonyAnimationsReturn {
  /** État actuel de l'animation */
  animationState: RoonyAnimationState;
  /** Déclencher une animation de Roony */
  triggerAnimation: (animation: RoonyAnimationType, duration?: number, position?: { x: number; y: number }) => void;
  /** Masquer Roony */
  hideRoony: () => void;
  /** Déclencher une animation contextuelle basée sur l'événement */
  triggerContextualAnimation: (context: RoonyContextType) => void;
}

// Types de contextes pour déclencher automatiquement les bonnes animations
export type RoonyContextType =
  | 'workflow-start'      // Début du workflow
  | 'step-complete'       // Étape terminée avec succès
  | 'idea-found'          // Une idée ou solution trouvée
  | 'processing'          // En cours de traitement/recherche
  | 'error'               // Erreur ou problème
  | 'final-result'        // Résultat final généré
  | 'user-input'          // Utilisateur saisit quelque chose
  | 'pointing-suggestion' // Pointer vers une suggestion
  | 'celebration'         // Célébration d'un succès
  | 'thinking';           // Réflexion en cours

// Animations disponibles pour la phase de réflexion (rotation intelligente)
const THINKING_ANIMATIONS: RoonyAnimationType[] = [
  'idea',           // Content avec une idée 💡
  'typing',         // Recherche sérieuse sur clavier
  'greeting',       // Salutation amicale
  'proud',          // Fier et confiant
  'pointing-up'     // Pointe vers le haut (inspiration)
];

// Index global pour la rotation des animations de réflexion
let thinkingAnimationIndex = 0;

/**
 * Hook personnalisé pour gérer les animations contextuelles de Roony
 * Permet de déclencher automatiquement les bonnes animations selon le contexte
 */
export const useRoonyAnimations = (): UseRoonyAnimationsReturn => {
  const [animationState, setAnimationState] = useState<RoonyAnimationState>({
    currentAnimation: 'idle',
    isVisible: false,
    position: null
  });

  const animationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fonction pour déclencher une animation spécifique
  const triggerAnimation = useCallback((
    animation: RoonyAnimationType, 
    duration: number = 3000,
    position?: { x: number; y: number }
  ) => {
    // Nettoyer le timeout précédent si il existe
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }

    // Définir la nouvelle animation
    setAnimationState({
      currentAnimation: animation,
      isVisible: animation !== 'idle',
      position: position || null
    });

    // Programmer la fin de l'animation si une durée est spécifiée
    if (duration > 0 && animation !== 'idle') {
      animationTimeoutRef.current = setTimeout(() => {
        setAnimationState(prev => ({
          ...prev,
          currentAnimation: 'idle',
          isVisible: false
        }));
      }, duration);
    }
  }, []);

  // Fonction pour masquer Roony immédiatement
  const hideRoony = useCallback(() => {
    if (animationTimeoutRef.current) {
      clearTimeout(animationTimeoutRef.current);
    }
    setAnimationState({
      currentAnimation: 'idle',
      isVisible: false,
      position: null
    });
  }, []);

  // Fonction pour obtenir la prochaine animation de réflexion (rotation)
  const getNextThinkingAnimation = useCallback((): RoonyAnimationType => {
    const animation = THINKING_ANIMATIONS[thinkingAnimationIndex];
    thinkingAnimationIndex = (thinkingAnimationIndex + 1) % THINKING_ANIMATIONS.length;
    console.log(`🎭 Animation de réflexion sélectionnée: ${animation} (${thinkingAnimationIndex}/${THINKING_ANIMATIONS.length})`);
    return animation;
  }, []);

  // Fonction pour déclencher des animations contextuelles
  const triggerContextualAnimation = useCallback((context: RoonyContextType) => {
    switch (context) {
      case 'workflow-start':
        // Roony salue et pointe vers le haut pour commencer
        triggerAnimation('greeting', 2000);
        setTimeout(() => {
          triggerAnimation('pointing-up', 3000);
        }, 2500);
        break;

      case 'step-complete':
        // Roony est content et fier
        triggerAnimation('proud', 2500);
        break;

      case 'idea-found':
        // Roony a une idée brillante 💡
        triggerAnimation('idea', 3000);
        break;

      case 'processing':
        // Roony utilise une animation de réflexion rotative
        const processingAnimation = getNextThinkingAnimation();
        triggerAnimation(processingAnimation, 3000);
        break;

      case 'error':
        // Roony exprime son mécontentement
        triggerAnimation('disagreement', 2500);
        break;

      case 'final-result':
        // Roony est fier du résultat final
        triggerAnimation('proud', 3500);
        break;

      case 'user-input':
        // Roony pointe vers la droite pour encourager la saisie
        triggerAnimation('pointing-right', 2000);
        break;

      case 'pointing-suggestion':
        // Roony pointe vers une suggestion
        triggerAnimation('pointing-right', 3000);
        break;

      case 'celebration':
        // Roony célèbre avec enthousiasme
        triggerAnimation('proud', 3000);
        break;

      case 'thinking':
        // Roony utilise une animation de réflexion rotative
        const thinkingAnimation = getNextThinkingAnimation();
        triggerAnimation(thinkingAnimation, 3000);
        break;

      default:
        // Animation par défaut
        triggerAnimation('greeting', 2000);
        break;
    }
  }, [triggerAnimation]);

  return {
    animationState,
    triggerAnimation,
    hideRoony,
    triggerContextualAnimation
  };
};

export default useRoonyAnimations;
