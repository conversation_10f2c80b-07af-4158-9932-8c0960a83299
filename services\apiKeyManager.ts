/**
 * Service de gestion intelligente des clés API OpenRouter
 * Rotation automatique, gestion des erreurs et optimisation des performances
 */

interface ApiKeyStats {
    keyId: string;
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    lastUsed: number;
    consecutiveFailures: number;
    isBlacklisted: boolean;
    blacklistUntil?: number;
    errorTypes: Record<string, number>; // Compteur par type d'erreur (400, 429, 503, etc.)
}

interface ApiKeyConfig {
    key: string;
    name: string;
    priority: number; // 1 = haute priorité, 5 = basse priorité
}

class ApiKeyManager {
    private apiKeys: ApiKeyConfig[] = [];
    private keyStats: Map<string, ApiKeyStats> = new Map();
    private currentKeyIndex = 0;
    private readonly BLACKLIST_DURATION = 5 * 60 * 1000; // 5 minutes
    private readonly MAX_CONSECUTIVE_FAILURES = 3;
    private readonly RATE_LIMIT_COOLDOWN = 60 * 1000; // 1 minute pour 429

    constructor() {
        this.initializeApiKeys();
        this.loadStatsFromStorage();
        
        // Nettoyage périodique des statistiques
        setInterval(() => this.cleanupStats(), 10 * 60 * 1000); // Toutes les 10 minutes
    }

    /**
     * Initialise les clés API depuis les variables d'environnement et le fichier Tasks.md
     */
    private initializeApiKeys(): void {
        // Clés API depuis Tasks.md (14 clés disponibles)
        const taskKeys: ApiKeyConfig[] = [
            { key: '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', name: 'Workflow Agentique 2', priority: 1 },
            { key: '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', name: 'Workflow Agentique 3', priority: 1 },
            { key: '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', name: 'Workflow Agentique 4', priority: 1 },
            { key: '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', name: 'Workflow Agentique 5', priority: 1 },
            { key: '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', name: 'Workflow Agentique 6', priority: 2 },
            { key: '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', name: 'Workflow Agentique 7', priority: 2 },
            { key: '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', name: 'Workflow Agentique 8', priority: 2 },
            { key: '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', name: 'Workflow Agentique 9', priority: 2 },
            { key: '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', name: 'Workflow Agentique 10', priority: 3 },
            { key: '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', name: 'Workflow Agentique 11', priority: 3 },
            { key: '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', name: 'Workflow Agentique 12', priority: 3 },
            { key: '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', name: 'Workflow Agentique 13', priority: 4 },
            { key: '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', name: 'Workflow Agentique 14', priority: 4 },
            { key: '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', name: 'Workflow Agentique 15', priority: 5 }
        ];

        // Ajouter la clé principale si elle existe
        const mainApiKey = process.env.API_KEY || process.env.VITE_API_KEY;
        if (mainApiKey && mainApiKey.trim() !== '') {
            this.apiKeys.push({ key: mainApiKey, name: 'Clé Principale', priority: 1 });
        }

        // Ajouter les clés du fichier Tasks.md
        this.apiKeys.push(...taskKeys);

        // Initialiser les statistiques pour chaque clé
        this.apiKeys.forEach(config => {
            if (!this.keyStats.has(config.key)) {
                this.keyStats.set(config.key, {
                    keyId: config.name,
                    totalRequests: 0,
                    successfulRequests: 0,
                    failedRequests: 0,
                    lastUsed: 0,
                    consecutiveFailures: 0,
                    isBlacklisted: false,
                    errorTypes: {}
                });
            }
        });

        console.log(`🔑 ${this.apiKeys.length} clés API initialisées pour la rotation intelligente`);
    }

    /**
     * Obtient la prochaine clé API disponible avec rotation intelligente
     */
    public getNextApiKey(): { key: string; keyId: string } {
        const availableKeys = this.getAvailableKeys();
        
        if (availableKeys.length === 0) {
            console.warn('⚠️ Aucune clé API disponible, utilisation de la première clé en fallback');
            const fallbackKey = this.apiKeys[0];
            return { key: fallbackKey.key, keyId: fallbackKey.name };
        }

        // Sélection intelligente basée sur les performances et la priorité
        const bestKey = this.selectBestKey(availableKeys);
        
        console.log(`🔑 Clé sélectionnée: ${bestKey.keyId} (Priorité: ${this.getKeyConfig(bestKey.key)?.priority})`);
        return bestKey;
    }

    /**
     * Enregistre le résultat d'une requête API
     */
    public recordApiResult(apiKey: string, success: boolean, errorCode?: number, errorMessage?: string): void {
        const stats = this.keyStats.get(apiKey);
        if (!stats) return;

        stats.totalRequests++;
        stats.lastUsed = Date.now();

        if (success) {
            stats.successfulRequests++;
            stats.consecutiveFailures = 0;
            
            // Retirer de la liste noire si la requête réussit
            if (stats.isBlacklisted) {
                stats.isBlacklisted = false;
                delete stats.blacklistUntil;
                console.log(`✅ Clé ${stats.keyId} retirée de la liste noire après succès`);
            }
        } else {
            stats.failedRequests++;
            stats.consecutiveFailures++;

            // Enregistrer le type d'erreur
            if (errorCode) {
                stats.errorTypes[errorCode.toString()] = (stats.errorTypes[errorCode.toString()] || 0) + 1;
            }

            // Gestion spécifique selon le type d'erreur
            this.handleErrorType(stats, errorCode, errorMessage);
        }

        this.saveStatsToStorage();
    }

    /**
     * Gère les différents types d'erreurs
     */
    private handleErrorType(stats: ApiKeyStats, errorCode?: number, errorMessage?: string): void {
        const now = Date.now();

        switch (errorCode) {
            case 429: // Too Many Requests
                stats.isBlacklisted = true;
                stats.blacklistUntil = now + this.RATE_LIMIT_COOLDOWN;
                console.log(`⏳ Clé ${stats.keyId} mise en liste noire pour 1 minute (429 - Rate Limit)`);
                break;

            case 400: // Bad Request - Provider Error
                if (errorMessage?.includes('Provider returned error')) {
                    // Erreur côté provider, liste noire temporaire
                    stats.isBlacklisted = true;
                    stats.blacklistUntil = now + (2 * 60 * 1000); // 2 minutes
                    console.log(`🚫 Clé ${stats.keyId} mise en liste noire pour 2 minutes (400 - Provider Error)`);
                }
                break;

            case 503: // Service Unavailable
                stats.isBlacklisted = true;
                stats.blacklistUntil = now + (3 * 60 * 1000); // 3 minutes
                console.log(`🔧 Clé ${stats.keyId} mise en liste noire pour 3 minutes (503 - Service Unavailable)`);
                break;

            default:
                // Pour les autres erreurs, liste noire après plusieurs échecs consécutifs
                if (stats.consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES) {
                    stats.isBlacklisted = true;
                    stats.blacklistUntil = now + this.BLACKLIST_DURATION;
                    console.log(`❌ Clé ${stats.keyId} mise en liste noire pour 5 minutes (${stats.consecutiveFailures} échecs consécutifs)`);
                }
                break;
        }
    }

    /**
     * Obtient les clés disponibles (non blacklistées)
     */
    private getAvailableKeys(): { key: string; keyId: string }[] {
        const now = Date.now();
        
        return this.apiKeys
            .filter(config => {
                const stats = this.keyStats.get(config.key);
                if (!stats) return true;

                // Vérifier si la liste noire a expiré
                if (stats.isBlacklisted && stats.blacklistUntil && now > stats.blacklistUntil) {
                    stats.isBlacklisted = false;
                    delete stats.blacklistUntil;
                    console.log(`🔓 Clé ${stats.keyId} retirée automatiquement de la liste noire`);
                }

                return !stats.isBlacklisted;
            })
            .map(config => ({ key: config.key, keyId: config.name }));
    }

    /**
     * Sélectionne la meilleure clé basée sur les performances et la priorité
     */
    private selectBestKey(availableKeys: { key: string; keyId: string }[]): { key: string; keyId: string } {
        // Trier par priorité puis par taux de succès
        const sortedKeys = availableKeys.sort((a, b) => {
            const configA = this.getKeyConfig(a.key);
            const configB = this.getKeyConfig(b.key);
            const statsA = this.keyStats.get(a.key);
            const statsB = this.keyStats.get(b.key);

            // Priorité d'abord
            const priorityDiff = (configA?.priority || 5) - (configB?.priority || 5);
            if (priorityDiff !== 0) return priorityDiff;

            // Puis taux de succès
            const successRateA = statsA ? (statsA.successfulRequests / Math.max(statsA.totalRequests, 1)) : 1;
            const successRateB = statsB ? (statsB.successfulRequests / Math.max(statsB.totalRequests, 1)) : 1;
            
            return successRateB - successRateA;
        });

        return sortedKeys[0];
    }

    /**
     * Obtient la configuration d'une clé
     */
    private getKeyConfig(key: string): ApiKeyConfig | undefined {
        return this.apiKeys.find(config => config.key === key);
    }

    /**
     * Sauvegarde les statistiques dans le localStorage
     */
    private saveStatsToStorage(): void {
        try {
            const statsData = Array.from(this.keyStats.entries());
            localStorage.setItem('api_key_stats', JSON.stringify(statsData));
        } catch (error) {
            console.warn('⚠️ Impossible de sauvegarder les statistiques des clés API:', error);
        }
    }

    /**
     * Charge les statistiques depuis le localStorage
     */
    private loadStatsFromStorage(): void {
        try {
            const savedStats = localStorage.getItem('api_key_stats');
            if (savedStats) {
                const statsData = JSON.parse(savedStats);
                this.keyStats = new Map(statsData);
                console.log('📊 Statistiques des clés API chargées depuis le cache');
            }
        } catch (error) {
            console.warn('⚠️ Impossible de charger les statistiques des clés API:', error);
        }
    }

    /**
     * Nettoie les anciennes statistiques
     */
    private cleanupStats(): void {
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 heures

        for (const [key, stats] of this.keyStats.entries()) {
            if (now - stats.lastUsed > maxAge && stats.totalRequests === 0) {
                this.keyStats.delete(key);
            }
        }

        this.saveStatsToStorage();
    }

    /**
     * Obtient les statistiques détaillées
     */
    public getStats(): Record<string, ApiKeyStats> {
        const stats: Record<string, ApiKeyStats> = {};
        for (const [key, stat] of this.keyStats.entries()) {
            const config = this.getKeyConfig(key);
            stats[config?.name || 'Clé inconnue'] = stat;
        }
        return stats;
    }

    /**
     * Réinitialise toutes les listes noires (pour debug)
     */
    public resetBlacklists(): void {
        for (const stats of this.keyStats.values()) {
            stats.isBlacklisted = false;
            delete stats.blacklistUntil;
            stats.consecutiveFailures = 0;
        }
        console.log('🔄 Toutes les listes noires ont été réinitialisées');
        this.saveStatsToStorage();
    }
}

// Instance singleton
export const apiKeyManager = new ApiKeyManager();
