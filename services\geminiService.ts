import { WORKFLOW_STEPS, OPENROUTER_MODELS, MODEL_DETECTION_CONFIG, LANGUAGE_VALIDATION_CONFIG, OPENROUTER_API_URL } from '../constants';
import { modelDetectionService } from './modelDetectionService';
import { languageValidationService, type LanguageValidationResult } from './languageValidationService';
import { translationService } from './translationService';
import { apiKeyManager } from './apiKeyManager';
import type { Step, Message } from '../types';

// Les clés API sont maintenant gérées par le service apiKeyManager
// pour une rotation intelligente et une gestion d'erreurs optimisée



// Utilisation du service de détection des modèles

/**
 * Sélectionne intelligemment un modèle pour une tâche donnée
 * Avec rotation automatique et vérification de disponibilité
 */
function selectModelForTask(task: Step['task'], excludeModels: string[] = []): string {
    const modelsForTask = OPENROUTER_MODELS[task];

    // Filtrer les modèles exclus et vérifier la disponibilité via le service
    const availableModels = modelsForTask.filter(model =>
        !excludeModels.includes(model) && modelDetectionService.isModelAvailable(model)
    );

    if (availableModels.length === 0) {
        console.warn(`⚠️ Aucun modèle disponible pour la tâche: ${task}`);
        // Fallback vers le premier modèle de la liste originale
        return modelsForTask[0];
    }

    // Sélection intelligente: varier les modèles pour optimiser le raisonnement agentique
    const selectedModel = availableModels[Math.floor(Math.random() * availableModels.length)];
    console.log(`🤖 Modèle sélectionné pour ${task}: ${selectedModel}`);

    return selectedModel;
}

/**
 * Fonction pour nettoyer le texte des symboles de réflexion indésirables
 */
const cleanThinkingSymbols = (text: string): string => {
    // Supprimer les symboles de réflexion bizarres comme ◁think▷, <think>, etc.
    return text
        .replace(/◁think▷/gi, '')
        .replace(/<think>/gi, '')
        .replace(/<\/think>/gi, '')
        .replace(/\[think\]/gi, '')
        .replace(/\[\/think\]/gi, '')
        .replace(/◁.*?▷/g, '')
        .replace(/【.*?】/g, '')
        .trim();
};

/**
 * Fonction principale pour envoyer un message à l'IA avec rotation automatique des modèles
 * et validation de langue française
 */
export async function sendMessageToAI(
  messages: { role: string; content: string }[],
  task: Step['task']
): Promise<{ content: string; modelUsed: string; languageValidation?: LanguageValidationResult }> {
    // Utilisation du gestionnaire de clés API pour la rotation intelligente
    console.log('🔄 Démarrage de la requête avec rotation intelligente des clés API');

    // Mise à jour de la cache des modèles via le service de détection
    await modelDetectionService.updateModelsIfNeeded();

    const excludedModels: string[] = [];
    let lastError: Error | null = null;
    let languageRetryCount = 0;

    // Tentatives avec rotation des modèles et validation de langue
    for (let attempt = 0; attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES; attempt++) {
        const model = selectModelForTask(task, excludedModels);

        // Obtenir une clé API via le gestionnaire avec rotation intelligente
        const { key: apiKey, keyId } = apiKeyManager.getNextApiKey();

        try {
            console.log(`🚀 Tentative ${attempt + 1}/${MODEL_DETECTION_CONFIG.MAX_RETRIES} avec le modèle: ${model} (Clé: ${keyId})`);

            const response = await fetch(OPENROUTER_API_URL, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                    // Headers recommandés par OpenRouter pour l'accès aux modèles gratuits
                    'HTTP-Referer': 'https://agentic-workflow-studio.web.app',
                    'X-Title': 'Agentic Workflow Studio',
                },
                body: JSON.stringify({
                    model: model,
                    messages: messages,
                    temperature: 0.7,
                    top_p: 0.95,
                }),
            });

            if (!response.ok) {
                const errorBody = await response.json().catch(() => ({
                    error: { message: 'Impossible de lire la réponse d\'erreur de l\'API.' }
                }));

                const errorMsg = `Erreur API OpenRouter: ${response.status} ${response.statusText} - ${errorBody.error?.message || 'Erreur inconnue'}`;
                console.warn(`❌ Échec avec ${model} (Clé: ${keyId}): ${errorMsg}`);

                // Enregistrer l'échec dans le gestionnaire de clés
                apiKeyManager.recordApiResult(apiKey, false, response.status, errorBody.error?.message);

                // Exclure ce modèle pour les prochaines tentatives
                excludedModels.push(model);
                lastError = new Error(errorMsg);

                // Attendre avant la prochaine tentative (délai adaptatif selon l'erreur)
                let retryDelay = MODEL_DETECTION_CONFIG.RETRY_DELAY;
                if (response.status === 429) {
                    retryDelay = 2000; // 2 secondes pour rate limit
                } else if (response.status === 503) {
                    retryDelay = 3000; // 3 secondes pour service unavailable
                }

                if (attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
                continue;
            }

            const data = await response.json();
            const rawContent = data.choices[0]?.message?.content || "Désolé, je n'ai pas pu générer une réponse.";
            const content = cleanThinkingSymbols(rawContent);

            // Validation de langue et traduction automatique si activée
            if (LANGUAGE_VALIDATION_CONFIG.ENABLE_STRICT_VALIDATION) {
                const languageValidation = languageValidationService.validateFrenchResponse(content, model);

                console.log(`📊 Validation linguistique: ${languageValidation.detectedLanguage} (confiance: ${languageValidation.confidence.toFixed(1)}%)`);

                // NOUVELLE APPROCHE: Traduction automatique au lieu de rejet
                let finalContent = content;
                let wasTranslated = false;

                // Si la réponse n'est pas française, on la traduit automatiquement
                if (!languageValidation.isValid) {
                    console.log(`🔄 Traduction automatique nécessaire pour le modèle: ${model}`);

                    try {
                        const translationResult = await translationService.translateToFrench(
                            content,
                            languageValidation.detectedLanguage,
                            languageValidation.confidence
                        );

                        if (translationResult.wasTranslated) {
                            finalContent = cleanThinkingSymbols(translationResult.translatedText);
                            wasTranslated = true;
                            console.log(`✅ Traduction réussie avec le modèle: ${model}`);
                        } else {
                            console.log(`ℹ️ Pas de traduction nécessaire pour: ${model}`);
                        }
                    } catch (translationError) {
                        console.warn(`⚠️ Erreur de traduction pour ${model}:`, translationError);
                        // En cas d'erreur de traduction, on garde le contenu original
                    }
                }

                // Enregistrer le succès dans le gestionnaire de clés
                apiKeyManager.recordApiResult(apiKey, true);

                // Log du résultat final
                if (languageValidation.isValid) {
                    console.log(`✅ Succès avec le modèle: ${model} (Clé: ${keyId}) - français validé, confiance: ${languageValidation.confidence.toFixed(1)}%`);
                } else if (wasTranslated) {
                    console.log(`✅ Succès avec traduction: ${model} (Clé: ${keyId}) - ${languageValidation.detectedLanguage} → français`);
                } else {
                    console.warn(`⚠️ Réponse acceptée sans traduction: ${model} (Clé: ${keyId}) - ${languageValidation.detectedLanguage}, confiance: ${languageValidation.confidence.toFixed(1)}%`);
                }

                return {
                    content: finalContent,
                    modelUsed: model,
                    languageValidation: {
                        ...languageValidation,
                        wasTranslated,
                        finalContent
                    } as any
                };
            }

            // Enregistrer le succès dans le gestionnaire de clés (cas sans validation linguistique)
            apiKeyManager.recordApiResult(apiKey, true);

            console.log(`✅ Succès avec le modèle: ${model} (Clé: ${keyId})`);
            return { content, modelUsed: model };

        } catch (error) {
            console.warn(`❌ Erreur avec le modèle ${model} (Clé: ${keyId}):`, error);

            // Enregistrer l'échec dans le gestionnaire de clés
            apiKeyManager.recordApiResult(apiKey, false, undefined, error instanceof Error ? error.message : 'Erreur inconnue');

            excludedModels.push(model);
            lastError = error as Error;

            // Attendre avant la prochaine tentative
            if (attempt < MODEL_DETECTION_CONFIG.MAX_RETRIES - 1) {
                await new Promise(resolve => setTimeout(resolve, MODEL_DETECTION_CONFIG.RETRY_DELAY));
            }
        }
    }

    // Si toutes les tentatives ont échoué
    const finalError = lastError || new Error('Tous les modèles ont échoué');
    console.error(`💥 Échec complet après ${MODEL_DETECTION_CONFIG.MAX_RETRIES} tentatives:`, finalError);
    throw finalError;
}

export const generateSystemPrompt = (stepIndex: number, problem: string): string => {
  const step = WORKFLOW_STEPS[stepIndex];
  if (!step) return `IMPORTANT: RÉPONDEZ UNIQUEMENT EN FRANÇAIS. Votre tâche est de résumer la conversation à propos de : ${problem}`;

  const commonInstruction = `
INSTRUCTION CRITIQUE: VOUS DEVEZ RÉPONDRE UNIQUEMENT EN FRANÇAIS. Aucune phrase, mot ou expression en anglais n'est autorisé.

Vous êtes un agent IA expert, guidant un utilisateur à travers un workflow de 14 étapes pour résoudre un problème complexe.
Votre personnalité est celle d'un stratège serviable, structuré et créatif.
Le problème principal de l'utilisateur est : "${problem}"

Nous sommes à l'ÉTAPE ${step.id}: **${step.title}**.
Description de cette étape : ${step.description}.
Techniques clés à considérer pour cette étape : ${step.techniques.join(', ')}.

RAPPEL IMPORTANT: Toute votre réponse doit être exclusivement en français, avec un vocabulaire français approprié.
`;

  if (stepIndex === 0) {
    return `
${commonInstruction}
Votre tâche est de commencer l'étape "Définition du Problème". Analysez la description initiale du problème par l'utilisateur.
Posez des questions clarifiantes pour l'aider à définir le problème plus précisément.
Gardez votre réponse concise et terminez en encourageant l'utilisateur à fournir ces détails.

OBLIGATION ABSOLUE: Rédigez votre réponse entièrement en français, sans aucun mot anglais.
`;
  }

  const nextStep = WORKFLOW_STEPS[stepIndex + 1];
  const finalPrompt = `
${commonInstruction}
Votre tâche est de générer la réponse pour l'étape actuelle (${step.title}).
- Analysez la dernière réponse de l'utilisateur et fournissez une synthèse ou une action pour l'étape actuelle.
- Si vous avez besoin de plus d'informations, posez des questions ciblées.
- Concluez en introduisant la prochaine étape : "${nextStep?.title || 'Génération du Prompt Final'}".
- Restez concis et focalisé sur l'étape en cours.
- Formatez votre réponse en markdown pour plus de clarté.

EXIGENCE LINGUISTIQUE: Votre réponse complète doit être rédigée en français uniquement. Utilisez un vocabulaire français riche et approprié.
`;
  return finalPrompt;
};

export const generateFinalOutput = async (conversation: Message[]): Promise<string> => {
    const finalSystemPrompt = `
    INSTRUCTION CRITIQUE: RÉPONDEZ UNIQUEMENT EN FRANÇAIS. Aucun mot ou expression en anglais n'est autorisé.

    Vous êtes un agent IA expert en synthèse finale. Votre tâche est d'analyser l'intégralité d'une conversation de workflow et de produire deux livrables distincts.
    1.  **Le Prompt Optimisé** : Un prompt final, puissant et autonome, prêt à être utilisé dans n'importe quel LLM. Il doit encapsuler l'objectif final et toutes les contraintes et nuances découvertes pendant la conversation.
    2.  **La Méta-Analyse de la Construction** : Une explication sur la manière dont vous avez construit ce prompt, en justifiant vos choix (structure, mots-clés, techniques utilisées) en vous basant sur la conversation.

    Séparez les deux sections par '---'.
    La sortie doit être structurée exactement comme suit:

    ### Le Prompt Optimisé

    [Votre prompt final ici]

    ---

    ### Méta-Analyse de la Construction

    [Votre analyse ici]

    RAPPEL OBLIGATOIRE: Toute votre réponse, y compris le prompt optimisé et la méta-analyse, doit être rédigée exclusivement en français.
    `;

    const conversationHistory = conversation.map(m => ({
        role: m.sender === 'user' ? 'user' : 'assistant',
        content: m.text
    }));

    const messages = [
        { role: 'system', content: finalSystemPrompt },
        ...conversationHistory
    ];
    
    // This call will now use the robust API key retrieval
    const { content } = await sendMessageToAI(messages, 'synthèse');
    return content;
};
