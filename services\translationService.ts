import { OPENROUTER_MODELS } from '../constants';
import { apiKeyManager } from './apiKeyManager';

// Configuration du service de traduction
export const TRANSLATION_CONFIG = {
    // Modèles spécialisés pour la traduction (rapides et efficaces)
    TRANSLATION_MODELS: [
        "google/gemini-2.0-flash-exp:free",
        "qwen/qwen3-8b:free",
        "mistralai/mistral-7b-instruct:free",
        "google/gemma-2-9b-it:free"
    ],
    // Seuil de confiance pour déclencher la traduction
    TRANSLATION_THRESHOLD: 40, // Si confiance française < 40%, on traduit
    // Timeout pour les requêtes de traduction
    TRANSLATION_TIMEOUT: 10000, // 10 secondes
    // Nombre maximum de tentatives de traduction
    MAX_TRANSLATION_RETRIES: 2
};

const OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions";

interface TranslationResult {
    translatedText: string;
    wasTranslated: boolean;
    originalLanguage: 'english' | 'mixed' | 'unknown';
    translationModel: string;
    confidence: number;
}

class TranslationService {
    private stats = {
        totalTranslations: 0,
        successfulTranslations: 0,
        failedTranslations: 0,
        averageTranslationTime: 0
    };

    /**
     * Traduit automatiquement un texte en français si nécessaire
     */
    public async translateToFrench(
        text: string, 
        detectedLanguage: 'french' | 'english' | 'mixed' | 'unknown',
        confidence: number
    ): Promise<TranslationResult> {
        const startTime = Date.now();

        // Si le texte est déjà en français avec une confiance suffisante, pas de traduction
        if (detectedLanguage === 'french' && confidence >= TRANSLATION_CONFIG.TRANSLATION_THRESHOLD) {
            return {
                translatedText: text,
                wasTranslated: false,
                originalLanguage: detectedLanguage as any,
                translationModel: 'none',
                confidence
            };
        }

        // Sinon, on traduit
        console.log(`🔄 Traduction nécessaire: langue détectée = ${detectedLanguage}, confiance = ${confidence.toFixed(1)}%`);

        try {
            const translatedText = await this.performTranslation(text);
            const translationTime = Date.now() - startTime;
            
            // Mettre à jour les statistiques
            this.updateStats(true, translationTime);

            console.log(`✅ Traduction réussie en ${translationTime}ms`);

            return {
                translatedText,
                wasTranslated: true,
                originalLanguage: detectedLanguage as any,
                translationModel: 'auto-selected',
                confidence: 95 // Confiance élevée après traduction
            };

        } catch (error) {
            console.error('❌ Erreur lors de la traduction:', error);
            this.updateStats(false, Date.now() - startTime);

            // En cas d'échec, retourner le texte original
            return {
                translatedText: text,
                wasTranslated: false,
                originalLanguage: detectedLanguage as any,
                translationModel: 'failed',
                confidence
            };
        }
    }

    /**
     * Effectue la traduction avec rotation des modèles
     */
    private async performTranslation(text: string): Promise<string> {
        // Utilisation du gestionnaire de clés API pour la traduction
        console.log('🔄 Démarrage de la traduction avec rotation des clés API');

        const translationPrompt = `INSTRUCTION CRITIQUE: Traduisez UNIQUEMENT le texte suivant en français. 

RÈGLES STRICTES:
- Traduisez SEULEMENT le contenu, ne pas ajouter d'explications
- Conservez le sens, le ton et la structure originale
- Utilisez un français naturel et fluide
- Ne pas mentionner que c'est une traduction
- Répondez UNIQUEMENT avec la traduction

TEXTE À TRADUIRE:
${text}

TRADUCTION EN FRANÇAIS:`;

        const messages = [
            { role: 'system', content: 'Vous êtes un traducteur expert. Traduisez uniquement le texte demandé en français, sans commentaires.' },
            { role: 'user', content: translationPrompt }
        ];

        // Essayer avec différents modèles de traduction
        for (const model of TRANSLATION_CONFIG.TRANSLATION_MODELS) {
            // Obtenir une clé API via le gestionnaire
            const { key: apiKey, keyId } = apiKeyManager.getNextApiKey();

            try {
                console.log(`🔄 Tentative de traduction avec: ${model} (Clé: ${keyId})`);

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), TRANSLATION_CONFIG.TRANSLATION_TIMEOUT);

                const response = await fetch(OPENROUTER_API_URL, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': 'https://agentic-workflow-studio.web.app',
                        'X-Title': 'Agentic Workflow Studio - Translation',
                    },
                    body: JSON.stringify({
                        model: model,
                        messages: messages,
                        temperature: 0.3, // Température basse pour traduction précise
                        max_tokens: Math.min(text.length * 2, 4000), // Limite raisonnable
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    console.warn(`⚠️ Modèle ${model} indisponible: ${response.status} (Clé: ${keyId})`);

                    // Enregistrer l'échec dans le gestionnaire de clés
                    apiKeyManager.recordApiResult(apiKey, false, response.status, 'Modèle indisponible pour traduction');
                    continue;
                }

                const data = await response.json();
                const translatedText = data.choices[0]?.message?.content?.trim();

                if (translatedText && translatedText.length > 0) {
                    console.log(`✅ Traduction réussie avec: ${model} (Clé: ${keyId})`);

                    // Enregistrer le succès dans le gestionnaire de clés
                    apiKeyManager.recordApiResult(apiKey, true);
                    return translatedText;
                }

            } catch (error) {
                console.warn(`⚠️ Erreur avec le modèle ${model} (Clé: ${keyId}):`, error);

                // Enregistrer l'échec dans le gestionnaire de clés
                apiKeyManager.recordApiResult(apiKey, false, undefined, error instanceof Error ? error.message : 'Erreur de traduction');
                continue;
            }
        }

        throw new Error("Tous les modèles de traduction ont échoué");
    }

    /**
     * Met à jour les statistiques de traduction
     */
    private updateStats(success: boolean, translationTime: number) {
        this.stats.totalTranslations++;
        
        if (success) {
            this.stats.successfulTranslations++;
            // Calcul de la moyenne mobile du temps de traduction
            const previousAverage = this.stats.averageTranslationTime;
            const successCount = this.stats.successfulTranslations;
            this.stats.averageTranslationTime = 
                (previousAverage * (successCount - 1) + translationTime) / successCount;
        } else {
            this.stats.failedTranslations++;
        }
    }

    /**
     * Obtient les statistiques de traduction
     */
    public getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalTranslations > 0 
                ? (this.stats.successfulTranslations / this.stats.totalTranslations) * 100 
                : 0
        };
    }

    /**
     * Réinitialise les statistiques
     */
    public resetStats() {
        this.stats = {
            totalTranslations: 0,
            successfulTranslations: 0,
            failedTranslations: 0,
            averageTranslationTime: 0
        };
    }

    /**
     * Génère un rapport de traduction
     */
    public generateTranslationReport(): string {
        const stats = this.getStats();
        return `📊 RAPPORT DE TRADUCTION AUTOMATIQUE

🔢 Statistiques Globales:
- Traductions totales: ${stats.totalTranslations}
- Traductions réussies: ${stats.successfulTranslations}
- Traductions échouées: ${stats.failedTranslations}
- Taux de succès: ${stats.successRate.toFixed(1)}%
- Temps moyen: ${stats.averageTranslationTime.toFixed(0)}ms

🎯 Configuration:
- Seuil de traduction: ${TRANSLATION_CONFIG.TRANSLATION_THRESHOLD}%
- Modèles utilisés: ${TRANSLATION_CONFIG.TRANSLATION_MODELS.length}
- Timeout: ${TRANSLATION_CONFIG.TRANSLATION_TIMEOUT}ms

📈 Performance:
${stats.successRate >= 90 ? '🟢 Excellente' : stats.successRate >= 70 ? '🟡 Bonne' : '🔴 À améliorer'}

Généré le: ${new Date().toLocaleString('fr-FR')}`;
    }
}

// Instance singleton du service de traduction
export const translationService = new TranslationService();
